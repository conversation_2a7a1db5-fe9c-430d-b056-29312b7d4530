<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创业项目管理平台 - 苹果简约风格</title>
    <link rel="stylesheet" href="frontend/shared/css/main-theme.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="nav">
        <div class="container flex justify-between items-center">
            <div class="text-xl font-bold text-primary">创业平台</div>
            <div class="flex gap-6">
                <a href="#" class="nav-item active" data-module="entrepreneurship">创业</a>
                <a href="#" class="nav-item" data-module="dating">相亲</a>
                <a href="#" class="nav-item">关于</a>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="container py-8">
        <!-- 标题区域 -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold mb-4" style="color: var(--text-primary);">苹果简约风格展示</h1>
            <p class="text-xl" style="color: var(--text-secondary);">简约而不简单，优雅的设计语言</p>
        </div>

        <!-- 按钮展示 -->
        <div class="card mb-8 subtle-hover">
            <div class="card-header">
                <h2 class="text-2xl font-bold" style="color: var(--text-primary);">按钮组件</h2>
            </div>
            <div class="card-body">
                <div class="flex gap-4 mb-6">
                    <button class="btn btn-primary">主要按钮</button>
                    <button class="btn btn-secondary">次要按钮</button>
                    <button class="btn btn-outline">边框按钮</button>
                </div>
                <div class="flex gap-4">
                    <button class="btn btn-primary btn-sm">小按钮</button>
                    <button class="btn btn-primary">默认按钮</button>
                    <button class="btn btn-primary btn-lg">大按钮</button>
                </div>
            </div>
        </div>

        <!-- 卡片展示 -->
        <div class="flex gap-8 mb-8">
            <div class="card subtle-hover" style="flex: 1;">
                <div class="card-header">
                    <h3 class="text-xl font-bold" style="color: var(--text-primary);">创业项目</h3>
                </div>
                <div class="card-body">
                    <p class="mb-6" style="color: var(--text-secondary); line-height: 1.6;">展示和管理创业项目，支持文件上传和项目展示。简洁的界面设计，专注于内容本身。</p>
                    <button class="btn btn-primary">进入模块</button>
                </div>
            </div>

            <div class="card subtle-hover" style="flex: 1;">
                <div class="card-header">
                    <h3 class="text-xl font-bold" style="color: var(--text-primary);">相亲交友</h3>
                </div>
                <div class="card-body">
                    <p class="mb-6" style="color: var(--text-secondary); line-height: 1.6;">未来扩展的相亲交友功能模块，采用相同的设计语言和交互模式。</p>
                    <button class="btn btn-outline">即将推出</button>
                </div>
            </div>
        </div>

        <!-- 表单展示 -->
        <div class="card mb-8">
            <div class="card-header">
                <h2 class="text-2xl font-bold" style="color: var(--text-primary);">表单组件</h2>
            </div>
            <div class="card-body">
                <form class="form-validate">
                    <div class="flex gap-8">
                        <div style="flex: 1;">
                            <div class="form-group">
                                <label class="form-label">用户名</label>
                                <input type="text" class="form-input" placeholder="请输入用户名" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">邮箱地址</label>
                                <input type="email" class="form-input" placeholder="请输入邮箱地址" required>
                            </div>
                        </div>
                        <div style="flex: 1;">
                            <div class="form-group">
                                <label class="form-label">密码</label>
                                <input type="password" class="form-input" placeholder="请输入密码" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">确认密码</label>
                                <input type="password" class="form-input" placeholder="请确认密码" required>
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">提交表单</button>
                </form>
            </div>
        </div>

        <!-- 文本样式展示 -->
        <div class="card mb-8">
            <div class="card-header">
                <h2 class="text-2xl font-bold" style="color: var(--text-primary);">文本样式</h2>
            </div>
            <div class="card-body">
                <h1 class="text-4xl font-bold mb-6" style="color: var(--text-primary);">这是 4XL 大标题</h1>
                <h2 class="text-2xl font-bold mb-4" style="color: var(--text-primary);">这是 2XL 标题</h2>
                <h3 class="text-xl font-bold mb-4" style="color: var(--text-primary);">这是 XL 标题</h3>
                <p class="text-lg mb-4" style="color: var(--text-secondary); line-height: 1.6;">这是大号正文文本，清晰易读，适合重要内容的展示。</p>
                <p class="mb-4" style="color: var(--text-secondary); line-height: 1.6;">这是默认正文文本，适合一般内容展示，保持良好的可读性。</p>
                <p class="text-sm" style="color: var(--text-tertiary);">这是小号文本，适合辅助信息和说明文字。</p>
            </div>
        </div>

        <!-- 功能演示 -->
        <div class="card">
            <div class="card-header">
                <h2 class="text-2xl font-bold" style="color: var(--text-primary);">交互功能</h2>
            </div>
            <div class="card-body">
                <div class="flex gap-4 mb-6">
                    <button class="btn btn-primary" onclick="MainTheme.showNotification('操作成功完成', 'success')">
                        成功通知
                    </button>
                    <button class="btn btn-secondary" onclick="MainTheme.showNotification('这是一条信息', 'info')">
                        信息通知
                    </button>
                    <button class="btn btn-outline" onclick="MainTheme.showNotification('发生了错误', 'error')">
                        错误通知
                    </button>
                </div>
                <p style="color: var(--text-secondary); line-height: 1.6;">点击按钮查看通知效果，所有交互都采用苹果官网的简约动画风格。</p>
            </div>
            <div class="card-footer">
                <p style="color: var(--text-tertiary); line-height: 1.6;">
                    <strong>设计特点：</strong>
                    简约而不简单、优雅的动画效果、清晰的视觉层次、模块化架构
                </p>
            </div>
        </div>
    </div>

    <!-- 底部信息 -->
    <footer class="text-center py-12" style="background-color: var(--gray-50); border-top: 1px solid var(--gray-200);">
        <div class="container">
            <p style="color: var(--text-secondary);">
                &copy; 2024 创业项目管理平台 · 苹果简约风格展示
            </p>
        </div>
    </footer>

    <script src="frontend/shared/js/main-theme.js"></script>
</body>
</html>
