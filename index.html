<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创业项目管理平台 - 苹果简约风格</title>
    <link rel="stylesheet" href="frontend/shared/css/main-theme.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="nav">
        <div class="container flex justify-between items-center">
            <div class="text-xl font-bold text-primary">创业平台</div>
            <div class="flex gap-6">
                <a href="#" class="nav-item active" data-module="entrepreneurship">创业</a>
                <a href="#" class="nav-item" data-module="dating">相亲</a>
                <a href="#" class="nav-item">关于</a>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="container py-8">
        <!-- 标题区域 -->
        <div class="text-center mb-16 floating-subtle">
            <h1 class="text-4xl font-bold mb-6 text-gradient">高级苹果风格展示</h1>
            <p class="text-xl" style="color: var(--text-secondary); line-height: 1.6;">精致的设计细节，现代化的视觉体验</p>
        </div>

        <!-- 按钮展示 -->
        <div class="card mb-8 subtle-hover premium-glow">
            <div class="card-header">
                <h2 class="text-2xl font-bold" style="color: var(--text-primary);">高级按钮组件</h2>
                <p style="color: var(--text-secondary); margin-top: 8px;">带有光泽效果和流畅动画的按钮</p>
            </div>
            <div class="card-body">
                <div class="flex gap-6 mb-8">
                    <button class="btn btn-primary">渐变主按钮</button>
                    <button class="btn btn-secondary">质感次按钮</button>
                    <button class="btn btn-outline">毛玻璃按钮</button>
                </div>
                <div class="flex gap-4">
                    <button class="btn btn-primary btn-sm">小按钮</button>
                    <button class="btn btn-primary">默认按钮</button>
                    <button class="btn btn-primary btn-lg">大按钮</button>
                </div>
            </div>
        </div>

        <!-- 卡片展示 -->
        <div class="flex gap-8 mb-12">
            <div class="card subtle-hover scale-on-hover" style="flex: 1;">
                <div class="card-header">
                    <h3 class="text-xl font-bold" style="color: var(--text-primary);">🚀 创业项目</h3>
                    <div style="width: 40px; height: 3px; background: var(--gradient-primary); border-radius: 2px; margin-top: 12px;"></div>
                </div>
                <div class="card-body">
                    <p class="mb-8" style="color: var(--text-secondary); line-height: 1.7;">展示和管理创业项目，支持文件上传和项目展示。精致的界面设计，专注于用户体验。</p>
                    <button class="btn btn-primary">进入模块</button>
                </div>
            </div>

            <div class="card subtle-hover scale-on-hover border-gradient" style="flex: 1;">
                <div class="card-header">
                    <h3 class="text-xl font-bold" style="color: var(--text-primary);">💕 相亲交友</h3>
                    <div style="width: 40px; height: 3px; background: linear-gradient(135deg, #ff6b6b, #feca57); border-radius: 2px; margin-top: 12px;"></div>
                </div>
                <div class="card-body">
                    <p class="mb-8" style="color: var(--text-secondary); line-height: 1.7;">未来扩展的相亲交友功能模块，采用相同的高级设计语言和交互模式。</p>
                    <button class="btn btn-outline">即将推出</button>
                </div>
            </div>
        </div>

        <!-- 表单展示 -->
        <div class="card mb-12 glass-morphism">
            <div class="card-header">
                <h2 class="text-2xl font-bold" style="color: var(--text-primary);">高级表单组件</h2>
                <p style="color: var(--text-secondary); margin-top: 8px;">毛玻璃效果和精致的交互反馈</p>
            </div>
            <div class="card-body">
                <form class="form-validate">
                    <div class="flex gap-10">
                        <div style="flex: 1;">
                            <div class="form-group">
                                <label class="form-label">用户名</label>
                                <input type="text" class="form-input" placeholder="请输入您的用户名" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">邮箱地址</label>
                                <input type="email" class="form-input" placeholder="<EMAIL>" required>
                            </div>
                        </div>
                        <div style="flex: 1;">
                            <div class="form-group">
                                <label class="form-label">密码</label>
                                <input type="password" class="form-input" placeholder="请设置安全密码" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">确认密码</label>
                                <input type="password" class="form-input" placeholder="请再次输入密码" required>
                            </div>
                        </div>
                    </div>
                    <div style="margin-top: 32px;">
                        <button type="submit" class="btn btn-primary btn-lg">创建账户</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 文本样式展示 -->
        <div class="card mb-12 subtle-hover">
            <div class="card-header">
                <h2 class="text-2xl font-bold" style="color: var(--text-primary);">高级文本样式</h2>
                <p style="color: var(--text-secondary); margin-top: 8px;">精致的字体层次和视觉效果</p>
            </div>
            <div class="card-body">
                <h1 class="text-4xl font-bold mb-8 text-gradient">渐变大标题效果</h1>
                <h2 class="text-2xl font-bold mb-6" style="color: var(--text-primary); letter-spacing: -0.02em;">精致的 2XL 标题</h2>
                <h3 class="text-xl font-bold mb-6" style="color: var(--text-primary); letter-spacing: -0.01em;">优雅的 XL 标题</h3>
                <p class="text-lg mb-6" style="color: var(--text-secondary); line-height: 1.7; letter-spacing: -0.01em;">这是大号正文文本，具有优秀的可读性和视觉层次感，适合重要内容的展示和阅读。</p>
                <p class="mb-6" style="color: var(--text-secondary); line-height: 1.7;">这是默认正文文本，保持良好的可读性和舒适的阅读体验，适合一般内容展示。</p>
                <p class="text-sm" style="color: var(--text-tertiary); line-height: 1.6;">这是小号文本，适合辅助信息、说明文字和次要内容的展示。</p>
            </div>
        </div>

        <!-- 功能演示 -->
        <div class="card premium-glow">
            <div class="card-header">
                <h2 class="text-2xl font-bold" style="color: var(--text-primary);">高级交互功能</h2>
                <p style="color: var(--text-secondary); margin-top: 8px;">流畅的动画和精致的视觉反馈</p>
            </div>
            <div class="card-body">
                <div class="flex gap-6 mb-8">
                    <button class="btn btn-primary" onclick="MainTheme.showNotification('✨ 操作成功完成', 'success')">
                        成功通知
                    </button>
                    <button class="btn btn-secondary" onclick="MainTheme.showNotification('ℹ️ 这是一条信息', 'info')">
                        信息通知
                    </button>
                    <button class="btn btn-outline" onclick="MainTheme.showNotification('⚠️ 发生了错误', 'error')">
                        错误通知
                    </button>
                </div>
                <p style="color: var(--text-secondary); line-height: 1.7;">点击按钮体验高级通知效果，所有交互都采用现代化的动画和视觉反馈设计。</p>
            </div>
            <div class="card-footer">
                <div style="display: flex; align-items: center; gap: 16px;">
                    <div style="flex: 1;">
                        <p style="color: var(--text-tertiary); line-height: 1.6;">
                            <strong>高级特性：</strong>
                            毛玻璃效果、渐变设计、流畅动画、精致阴影、模块化架构
                        </p>
                    </div>
                    <div style="width: 60px; height: 4px; background: var(--gradient-primary); border-radius: 2px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部信息 -->
    <footer class="text-center py-12" style="background-color: var(--gray-50); border-top: 1px solid var(--gray-200);">
        <div class="container">
            <p style="color: var(--text-secondary);">
                &copy; 2024 创业项目管理平台 · 苹果简约风格展示
            </p>
        </div>
    </footer>

    <script src="frontend/shared/js/main-theme.js"></script>
</body>
</html>
