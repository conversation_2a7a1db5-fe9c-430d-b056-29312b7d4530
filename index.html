<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创业项目管理平台 - 主风格展示</title>
    <link rel="stylesheet" href="frontend/shared/css/main-theme.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="nav">
        <div class="container flex justify-between items-center">
            <div class="text-xl font-bold text-primary">创业平台</div>
            <div class="flex gap-6">
                <a href="#" class="nav-item active" data-module="entrepreneurship">创业</a>
                <a href="#" class="nav-item" data-module="dating">相亲</a>
                <a href="#" class="nav-item">关于</a>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="container py-8">
        <!-- 标题区域 -->
        <div class="text-center mb-6">
            <h1 class="text-3xl font-bold mb-4 gradient-text floating-animation">苹果炫彩风格展示</h1>
            <p class="text-lg" style="color: white; text-shadow: 0 1px 2px rgba(0,0,0,0.3);">现代化的毛玻璃效果，支持模块化扩展</p>
        </div>

        <!-- 按钮展示 -->
        <div class="card mb-6 pulse-glow">
            <div class="card-header">
                <h2 class="text-xl font-bold" style="color: white;">炫彩按钮组件</h2>
            </div>
            <div class="card-body">
                <div class="flex gap-4 mb-4">
                    <button class="btn btn-primary">渐变主按钮</button>
                    <button class="btn btn-secondary">炫彩次按钮</button>
                    <button class="btn btn-outline">毛玻璃按钮</button>
                </div>
                <div class="flex gap-4 mb-4">
                    <button class="btn btn-primary btn-sm">小按钮</button>
                    <button class="btn btn-primary">默认按钮</button>
                    <button class="btn btn-primary btn-lg">大按钮</button>
                </div>
                <div class="rainbow-border p-4">
                    <p style="color: var(--gray-800); text-align: center; font-weight: 600;">炫彩边框效果展示</p>
                </div>
            </div>
        </div>

        <!-- 卡片展示 -->
        <div class="flex gap-6 mb-6">
            <div class="card floating-animation" style="flex: 1;">
                <div class="card-header">
                    <h3 class="text-lg font-bold" style="color: white;">🚀 创业项目</h3>
                </div>
                <div class="card-body">
                    <p class="mb-4" style="color: var(--gray-800);">展示和管理创业项目，支持文件上传和项目展示。毛玻璃效果让界面更加现代化。</p>
                    <button class="btn btn-primary">进入模块</button>
                </div>
            </div>

            <div class="card floating-animation" style="flex: 1; animation-delay: 0.5s;">
                <div class="card-header">
                    <h3 class="text-lg font-bold" style="color: white;">💕 相亲交友</h3>
                </div>
                <div class="card-body">
                    <p class="mb-4" style="color: var(--gray-800);">未来扩展的相亲交友功能模块，采用相同的设计语言。</p>
                    <button class="btn btn-outline">即将推出</button>
                </div>
            </div>
        </div>

        <!-- 表单展示 -->
        <div class="card mb-6 glass-effect">
            <div class="card-header">
                <h2 class="text-xl font-bold" style="color: white;">🎨 毛玻璃表单</h2>
            </div>
            <div class="card-body">
                <form class="form-validate">
                    <div class="flex gap-6">
                        <div style="flex: 1;">
                            <div class="form-group">
                                <label class="form-label">✨ 用户名</label>
                                <input type="text" class="form-input" placeholder="请输入用户名" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">📧 邮箱</label>
                                <input type="email" class="form-input" placeholder="请输入邮箱" required>
                            </div>
                        </div>
                        <div style="flex: 1;">
                            <div class="form-group">
                                <label class="form-label">🔒 密码</label>
                                <input type="password" class="form-input" placeholder="请输入密码" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">🔐 确认密码</label>
                                <input type="password" class="form-input" placeholder="请确认密码" required>
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">🚀 提交表单</button>
                </form>
            </div>
        </div>

        <!-- 文本样式展示 -->
        <div class="card mb-6">
            <div class="card-header">
                <h2 class="text-xl font-bold" style="color: white;">📝 炫彩文本样式</h2>
            </div>
            <div class="card-body">
                <h1 class="text-3xl font-bold mb-4 gradient-text">这是渐变 3XL 标题</h1>
                <h2 class="text-2xl font-bold mb-4" style="color: var(--gray-800);">这是 2XL 标题</h2>
                <h3 class="text-xl font-bold mb-4" style="color: var(--gray-800);">这是 XL 标题</h3>
                <p class="text-lg mb-4" style="color: var(--gray-800);">这是大号正文文本，在毛玻璃背景上清晰可读。</p>
                <p class="mb-4" style="color: var(--gray-800);">这是默认正文文本，适合一般内容展示。</p>
                <p class="text-sm" style="color: var(--gray-600);">这是小号文本，适合辅助信息展示。</p>
            </div>
        </div>

        <!-- 功能演示 -->
        <div class="card">
            <div class="card-header">
                <h2 class="text-xl font-bold" style="color: white;">⚡ 交互功能展示</h2>
            </div>
            <div class="card-body">
                <div class="flex gap-4 mb-4">
                    <button class="btn btn-primary" onclick="MainTheme.showNotification('🎉 这是成功消息', 'success')">
                        ✅ 成功通知
                    </button>
                    <button class="btn btn-secondary" onclick="MainTheme.showNotification('ℹ️ 这是信息消息', 'info')">
                        💬 信息通知
                    </button>
                    <button class="btn btn-outline" onclick="MainTheme.showNotification('❌ 这是错误消息', 'error')">
                        🚨 错误通知
                    </button>
                </div>
                <p class="text-sm" style="color: var(--gray-700);">点击按钮查看通知效果，所有按钮都有炫酷的悬浮和缩放动画。</p>
            </div>
            <div class="card-footer">
                <p class="text-sm" style="color: var(--gray-700);">
                    <strong>🎨 苹果风格特点：</strong>
                    毛玻璃效果、炫彩渐变、流畅动画、现代化设计、模块化架构
                </p>
            </div>
        </div>
    </div>

    <!-- 底部信息 -->
    <footer class="text-center py-8 text-gray-600">
        <div class="container">
            <p>&copy; 2024 创业项目管理平台. 主风格样式展示页面</p>
        </div>
    </footer>

    <script src="frontend/shared/js/main-theme.js"></script>
</body>
</html>
