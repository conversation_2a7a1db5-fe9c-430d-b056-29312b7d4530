/* 主风格样式 - 创业项目管理平台 */

/* CSS变量定义 - 苹果官网简约风格 */
:root {
  /* 苹果官网主色调 */
  --primary-color: #0071e3;
  --primary-hover: #0077ed;
  --primary-light: rgba(0, 113, 227, 0.08);

  /* 苹果官网色彩系统 */
  --blue: #0071e3;
  --green: #1d8348;
  --orange: #f56500;
  --red: #d70015;
  --purple: #8e44ad;

  /* 中性色 - 苹果官网风格 */
  --white: #ffffff;
  --gray-50: #fbfbfd;
  --gray-100: #f5f5f7;
  --gray-200: #e8e8ed;
  --gray-300: #d2d2d7;
  --gray-400: #86868b;
  --gray-500: #515154;
  --gray-600: #424245;
  --gray-700: #1d1d1f;
  --gray-800: #1d1d1f;
  --gray-900: #000000;

  /* 文本颜色 */
  --text-primary: #1d1d1f;
  --text-secondary: #86868b;
  --text-tertiary: #515154;
  
  /* 苹果官网字体系统 */
  --font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', Helvetica, Arial, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1.0625rem;
  --font-size-lg: 1.1875rem;
  --font-size-xl: 1.3125rem;
  --font-size-2xl: 1.6875rem;
  --font-size-3xl: 2.5rem;
  --font-size-4xl: 3.5rem;

  /* 苹果官网间距系统 */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;

  /* 苹果官网圆角 */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* 高级阴影系统 */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 1px 3px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.15), 0 12px 12px rgba(0, 0, 0, 0.22);
  --shadow-inner: inset 0 1px 4px rgba(0, 0, 0, 0.06);
  --shadow-glow: 0 0 0 1px rgba(0, 113, 227, 0.05), 0 0 0 4px rgba(0, 113, 227, 0.1);

  /* 高级过渡动画 */
  --transition-fast: 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0.0, 0.2, 1);
  --transition-bounce: 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* 渐变系统 */
  --gradient-subtle: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  --gradient-border: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  --gradient-primary: linear-gradient(135deg, #0071e3 0%, #005bb5 100%);
  --gradient-surface: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
}

/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.47059;
  color: var(--text-primary);
  background: linear-gradient(135deg, #fafbfc 0%, #f1f3f4 100%);
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: relative;
}

/* 高级背景纹理 */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(0, 113, 227, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(0, 113, 227, 0.02) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* 高级按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  font-size: var(--font-size-base);
  font-weight: 500;
  text-decoration: none;
  border: none;
  border-radius: var(--radius-2xl);
  cursor: pointer;
  transition: all var(--transition-normal);
  outline: none;
  min-height: 44px;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  letter-spacing: -0.01em;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn-secondary {
  background: var(--gradient-surface);
  color: var(--text-primary);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
}

.btn-secondary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  border-color: var(--gray-300);
}

.btn-outline {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: var(--primary-color);
  border: 1px solid rgba(0, 113, 227, 0.2);
  box-shadow: var(--shadow-sm);
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-glow);
  transform: translateY(-1px);
}

.btn-sm {
  padding: 8px 16px;
  font-size: var(--font-size-sm);
  min-height: 36px;
}

.btn-lg {
  padding: 16px 32px;
  font-size: var(--font-size-lg);
  min-height: 52px;
}

/* 高级卡片组件 */
.card {
  background: var(--gradient-surface);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid rgba(255, 255, 255, 0.8);
  position: relative;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--gradient-border);
}

.card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px) scale(1.01);
  border-color: rgba(255, 255, 255, 1);
}

.card-header {
  padding: var(--spacing-8);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  background: var(--gradient-subtle);
  position: relative;
}

.card-body {
  padding: var(--spacing-8);
  position: relative;
}

.card-footer {
  padding: var(--spacing-8);
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  background: var(--gradient-subtle);
  position: relative;
}

/* 高级导航组件 */
.nav {
  display: flex;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: saturate(180%) blur(20px);
  -webkit-backdrop-filter: saturate(180%) blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding: var(--spacing-4) 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 1px 0 rgba(255, 255, 255, 0.8), 0 1px 3px rgba(0, 0, 0, 0.05);
}

.nav-item {
  padding: var(--spacing-3) var(--spacing-5);
  color: var(--text-primary);
  text-decoration: none;
  transition: all var(--transition-normal);
  border-radius: var(--radius-lg);
  font-weight: 400;
  font-size: var(--font-size-base);
  position: relative;
  letter-spacing: -0.01em;
}

.nav-item::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  border-radius: 1px;
  transition: all var(--transition-normal);
  transform: translateX(-50%);
}

.nav-item:hover {
  color: var(--primary-color);
  background: rgba(0, 113, 227, 0.05);
}

.nav-item.active {
  color: var(--primary-color);
  font-weight: 600;
}

.nav-item.active::before {
  width: 20px;
}

/* 高级表单组件 */
.form-group {
  margin-bottom: var(--spacing-6);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-3);
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-base);
  letter-spacing: -0.01em;
}

.form-input {
  width: 100%;
  padding: 18px 24px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-xl);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  transition: all var(--transition-normal);
  font-family: var(--font-family);
  box-shadow: var(--shadow-inner);
  position: relative;
}

.form-input::placeholder {
  color: var(--text-secondary);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: var(--shadow-glow), var(--shadow-inner);
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
}

.form-input:hover:not(:focus) {
  border-color: rgba(0, 0, 0, 0.15);
  box-shadow: var(--shadow-sm), var(--shadow-inner);
}

/* 布局工具类 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-4 {
  gap: var(--spacing-4);
}

.gap-6 {
  gap: var(--spacing-6);
}

/* 文本工具类 */
.text-center {
  text-align: center;
}

.text-sm {
  font-size: var(--font-size-sm);
}

.text-lg {
  font-size: var(--font-size-lg);
}

.text-xl {
  font-size: var(--font-size-xl);
}

.text-2xl {
  font-size: var(--font-size-2xl);
}

.text-3xl {
  font-size: var(--font-size-3xl);
}

.text-4xl {
  font-size: var(--font-size-4xl);
}

.font-bold {
  font-weight: 700;
}

.text-primary {
  color: var(--primary-color);
}

.text-gray-600 {
  color: var(--gray-600);
}

/* 间距工具类 */
.p-4 {
  padding: var(--spacing-4);
}

.p-6 {
  padding: var(--spacing-6);
}

.py-8 {
  padding-top: var(--spacing-8);
  padding-bottom: var(--spacing-8);
}

.mb-4 {
  margin-bottom: var(--spacing-4);
}

.mb-6 {
  margin-bottom: var(--spacing-6);
}

.mt-8 {
  margin-top: var(--spacing-8);
}

/* 高级视觉特效 */
.glass-morphism {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.subtle-hover {
  transition: all var(--transition-normal);
}

.subtle-hover:hover {
  transform: translateY(-2px);
}

.premium-glow {
  position: relative;
}

.premium-glow::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg,
    rgba(0, 113, 227, 0.1) 0%,
    rgba(0, 113, 227, 0.05) 25%,
    rgba(0, 113, 227, 0.1) 50%,
    rgba(0, 113, 227, 0.05) 75%,
    rgba(0, 113, 227, 0.1) 100%);
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.premium-glow:hover::before {
  opacity: 1;
}

.floating-subtle {
  animation: floatingSubtle 6s ease-in-out infinite;
}

@keyframes floatingSubtle {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-3px); }
}

.scale-on-hover {
  transition: transform var(--transition-normal);
}

.scale-on-hover:hover {
  transform: scale(1.02);
}

.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.border-gradient {
  position: relative;
  background: var(--white);
  border-radius: var(--radius-2xl);
}

.border-gradient::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(135deg, rgba(0, 113, 227, 0.2), rgba(0, 113, 227, 0.05));
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-4);
  }

  .btn {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-xs);
  }

  .card-header,
  .card-body,
  .card-footer {
    padding: var(--spacing-4);
  }

  body {
    background-size: 200% 200%;
  }
}
