/* 主风格样式 - 创业项目管理平台 */

/* CSS变量定义 - 苹果官网简约风格 */
:root {
  /* 苹果官网主色调 */
  --primary-color: #0071e3;
  --primary-hover: #0077ed;
  --primary-light: rgba(0, 113, 227, 0.08);

  /* 苹果官网色彩系统 */
  --blue: #0071e3;
  --green: #1d8348;
  --orange: #f56500;
  --red: #d70015;
  --purple: #8e44ad;

  /* 中性色 - 苹果官网风格 */
  --white: #ffffff;
  --gray-50: #fbfbfd;
  --gray-100: #f5f5f7;
  --gray-200: #e8e8ed;
  --gray-300: #d2d2d7;
  --gray-400: #86868b;
  --gray-500: #515154;
  --gray-600: #424245;
  --gray-700: #1d1d1f;
  --gray-800: #1d1d1f;
  --gray-900: #000000;

  /* 文本颜色 */
  --text-primary: #1d1d1f;
  --text-secondary: #86868b;
  --text-tertiary: #515154;
  
  /* 苹果官网字体系统 */
  --font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', Helvetica, Arial, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1.0625rem;
  --font-size-lg: 1.1875rem;
  --font-size-xl: 1.3125rem;
  --font-size-2xl: 1.6875rem;
  --font-size-3xl: 2.5rem;
  --font-size-4xl: 3.5rem;

  /* 苹果官网间距系统 */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;

  /* 苹果官网圆角 */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* 苹果官网阴影系统 */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.15);

  /* 苹果官网过渡动画 */
  --transition-fast: 0.2s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
}

/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.47059;
  color: var(--text-primary);
  background-color: var(--white);
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 苹果官网按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 22px;
  font-size: var(--font-size-base);
  font-weight: 400;
  text-decoration: none;
  border: none;
  border-radius: var(--radius-2xl);
  cursor: pointer;
  transition: all var(--transition-fast);
  outline: none;
  min-height: 44px;
  white-space: nowrap;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-secondary {
  background-color: var(--gray-100);
  color: var(--text-primary);
}

.btn-secondary:hover {
  background-color: var(--gray-200);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-outline:hover {
  background-color: var(--primary-light);
}

.btn-sm {
  padding: 8px 16px;
  font-size: var(--font-size-sm);
  min-height: 36px;
}

.btn-lg {
  padding: 16px 32px;
  font-size: var(--font-size-lg);
  min-height: 52px;
}

/* 苹果官网卡片组件 */
.card {
  background-color: var(--white);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--spacing-8);
  border-bottom: 1px solid var(--gray-200);
}

.card-body {
  padding: var(--spacing-8);
}

.card-footer {
  padding: var(--spacing-8);
  border-top: 1px solid var(--gray-200);
  background-color: var(--gray-50);
}

/* 苹果官网导航组件 */
.nav {
  display: flex;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: saturate(180%) blur(20px);
  -webkit-backdrop-filter: saturate(180%) blur(20px);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--spacing-4) 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-item {
  padding: var(--spacing-3) var(--spacing-4);
  color: var(--text-primary);
  text-decoration: none;
  transition: all var(--transition-fast);
  border-radius: var(--radius-md);
  font-weight: 400;
  font-size: var(--font-size-base);
}

.nav-item:hover {
  color: var(--primary-color);
}

.nav-item.active {
  color: var(--primary-color);
  font-weight: 600;
}

/* 苹果官网表单组件 */
.form-group {
  margin-bottom: var(--spacing-6);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: 600;
  color: var(--text-primary);
  font-size: var(--font-size-base);
}

.form-input {
  width: 100%;
  padding: 16px 20px;
  background-color: var(--white);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-xl);
  font-size: var(--font-size-base);
  color: var(--text-primary);
  transition: all var(--transition-fast);
  font-family: var(--font-family);
}

.form-input::placeholder {
  color: var(--text-secondary);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px var(--primary-light);
}

/* 布局工具类 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-4 {
  gap: var(--spacing-4);
}

.gap-6 {
  gap: var(--spacing-6);
}

/* 文本工具类 */
.text-center {
  text-align: center;
}

.text-sm {
  font-size: var(--font-size-sm);
}

.text-lg {
  font-size: var(--font-size-lg);
}

.text-xl {
  font-size: var(--font-size-xl);
}

.text-2xl {
  font-size: var(--font-size-2xl);
}

.text-3xl {
  font-size: var(--font-size-3xl);
}

.text-4xl {
  font-size: var(--font-size-4xl);
}

.font-bold {
  font-weight: 700;
}

.text-primary {
  color: var(--primary-color);
}

.text-gray-600 {
  color: var(--gray-600);
}

/* 间距工具类 */
.p-4 {
  padding: var(--spacing-4);
}

.p-6 {
  padding: var(--spacing-6);
}

.py-8 {
  padding-top: var(--spacing-8);
  padding-bottom: var(--spacing-8);
}

.mb-4 {
  margin-bottom: var(--spacing-4);
}

.mb-6 {
  margin-bottom: var(--spacing-6);
}

.mt-8 {
  margin-top: var(--spacing-8);
}

/* 苹果官网简约特效 */
.subtle-hover {
  transition: all var(--transition-fast);
}

.subtle-hover:hover {
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-4);
  }

  .btn {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-xs);
  }

  .card-header,
  .card-body,
  .card-footer {
    padding: var(--spacing-4);
  }

  body {
    background-size: 200% 200%;
  }
}
