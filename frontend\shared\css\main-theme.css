/* 主风格样式 - 创业项目管理平台 */

/* CSS变量定义 - 苹果官网简约风格 */
:root {
  /* 苹果官网主色调 */
  --primary-color: #0071e3;
  --primary-hover: #0077ed;
  --primary-light: rgba(0, 113, 227, 0.08);

  /* 苹果官网色彩系统 */
  --blue: #0071e3;
  --green: #1d8348;
  --orange: #f56500;
  --red: #d70015;
  --purple: #8e44ad;

  /* 中性色 - 苹果官网风格 */
  --white: #ffffff;
  --gray-50: #fbfbfd;
  --gray-100: #f5f5f7;
  --gray-200: #e8e8ed;
  --gray-300: #d2d2d7;
  --gray-400: #86868b;
  --gray-500: #515154;
  --gray-600: #424245;
  --gray-700: #1d1d1f;
  --gray-800: #1d1d1f;
  --gray-900: #000000;

  /* 文本颜色 */
  --text-primary: #1d1d1f;
  --text-secondary: #86868b;
  --text-tertiary: #515154;
  
  /* 苹果官网字体系统 */
  --font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', Helvetica, Arial, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1.0625rem;
  --font-size-lg: 1.1875rem;
  --font-size-xl: 1.3125rem;
  --font-size-2xl: 1.6875rem;
  --font-size-3xl: 2.5rem;
  --font-size-4xl: 3.5rem;

  /* 苹果官网间距系统 */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;

  /* 苹果官网圆角 */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* 苹果官网阴影系统 */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.15);

  /* 苹果官网过渡动画 */
  --transition-fast: 0.2s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
}

/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.47059;
  color: var(--text-primary);
  background-color: var(--white);
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 苹果风格按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-sm);
  font-weight: 600;
  text-decoration: none;
  border: none;
  border-radius: var(--radius-xl);
  cursor: pointer;
  transition: all var(--transition-fast);
  outline: none;
  position: relative;
  overflow: hidden;
  backdrop-filter: var(--blur-light);
  -webkit-backdrop-filter: var(--blur-light);
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-lg), var(--shadow-glow);
}

.btn-secondary {
  background: var(--gradient-secondary);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-lg);
}

.btn-outline {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: var(--blur-light);
  -webkit-backdrop-filter: var(--blur-light);
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px) scale(1.02);
}

.btn-sm {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-lg);
}

/* 苹果风格卡片组件 */
.card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: var(--blur-light);
  -webkit-backdrop-filter: var(--blur-light);
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: var(--shadow-xl);
  background: rgba(255, 255, 255, 0.35);
}

.card-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
}

.card-body {
  padding: var(--spacing-6);
  color: var(--gray-800);
}

.card-footer {
  padding: var(--spacing-6);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
}

/* 苹果风格导航组件 */
.nav {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: var(--blur-heavy);
  -webkit-backdrop-filter: var(--blur-heavy);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: var(--spacing-4) 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-item {
  padding: var(--spacing-3) var(--spacing-6);
  color: white;
  text-decoration: none;
  transition: all var(--transition-fast);
  border-radius: var(--radius-lg);
  font-weight: 500;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.nav-item.active {
  background: var(--gradient-accent);
  color: white;
  box-shadow: var(--shadow-md);
}

/* 苹果风格表单组件 */
.form-group {
  margin-bottom: var(--spacing-5);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.form-input {
  width: 100%;
  padding: var(--spacing-4);
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: var(--blur-light);
  -webkit-backdrop-filter: var(--blur-light);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-xl);
  font-size: var(--font-size-base);
  color: var(--gray-800);
  transition: all var(--transition-fast);
}

.form-input::placeholder {
  color: rgba(0, 0, 0, 0.5);
}

.form-input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
  transform: scale(1.02);
}

/* 布局工具类 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-4 {
  gap: var(--spacing-4);
}

.gap-6 {
  gap: var(--spacing-6);
}

/* 文本工具类 */
.text-center {
  text-align: center;
}

.text-sm {
  font-size: var(--font-size-sm);
}

.text-lg {
  font-size: var(--font-size-lg);
}

.text-xl {
  font-size: var(--font-size-xl);
}

.text-2xl {
  font-size: var(--font-size-2xl);
}

.text-3xl {
  font-size: var(--font-size-3xl);
}

.font-bold {
  font-weight: 700;
}

.text-primary {
  color: var(--primary-color);
}

.text-gray-600 {
  color: var(--gray-600);
}

/* 间距工具类 */
.p-4 {
  padding: var(--spacing-4);
}

.p-6 {
  padding: var(--spacing-6);
}

.py-8 {
  padding-top: var(--spacing-8);
  padding-bottom: var(--spacing-8);
}

.mb-4 {
  margin-bottom: var(--spacing-4);
}

.mb-6 {
  margin-bottom: var(--spacing-6);
}

.mt-8 {
  margin-top: var(--spacing-8);
}

/* 苹果风格特效 */
.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: var(--blur-light);
  -webkit-backdrop-filter: var(--blur-light);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.floating-animation {
  animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite alternate;
}

@keyframes pulseGlow {
  from { box-shadow: 0 0 20px rgba(0, 122, 255, 0.3); }
  to { box-shadow: 0 0 30px rgba(0, 122, 255, 0.6); }
}

/* 炫彩边框动画 */
.rainbow-border {
  position: relative;
  background: linear-gradient(45deg, #ff0000, #ff7300, #fffb00, #48ff00, #00ffd5, #002bff, #7a00ff, #ff00c8, #ff0000);
  background-size: 400%;
  border-radius: var(--radius-xl);
  animation: rainbowBorder 3s linear infinite;
}

.rainbow-border::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: var(--radius-xl);
  z-index: -1;
}

@keyframes rainbowBorder {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-4);
  }

  .btn {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-xs);
  }

  .card-header,
  .card-body,
  .card-footer {
    padding: var(--spacing-4);
  }

  body {
    background-size: 200% 200%;
  }
}
