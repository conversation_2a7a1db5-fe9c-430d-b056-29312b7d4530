# 创业项目管理平台 - 完整文件夹架构

## 项目概述
基于 HTML、CSS、JS、PHP、MySQL 的创业项目管理平台，支持项目代码上传、管理和展示功能。

## 完整文件夹结构

```
entrepreneurship/                    # 项目根目录
├── README.md                       # 项目说明文档
│
├── frontend/                       # 前端代码目录
│   ├── assets/                     # 静态资源
│   │   ├── fonts/                  # 字体文件
│   │   ├── icons/                  # 图标文件
│   │   └── vendor/                 # 第三方库文件
│   ├── components/                 # 可复用组件
│   ├── css/                        # 样式文件
│   │   ├── components/             # 组件样式
│   │   ├── pages/                  # 页面样式
│   │   └── themes/                 # 主题样式
│   ├── images/                     # 图片资源
│   ├── js/                         # JavaScript文件
│   │   ├── api/                    # API调用模块
│   │   ├── modules/                # 功能模块
│   │   └── utils/                  # 工具函数
│   └── pages/                      # 页面文件
│
├── backend/                        # 后端代码目录
│   ├── api/                        # API接口
│   │   ├── v1/                     # API版本1
│   │   ├── auth/                   # 认证相关API
│   │   ├── upload/                 # 上传相关API
│   │   └── project/                # 项目相关API
│   ├── controllers/                # 控制器
│   │   ├── auth/                   # 认证控制器
│   │   ├── project/                # 项目控制器
│   │   ├── upload/                 # 上传控制器
│   │   └── admin/                  # 管理员控制器
│   ├── models/                     # 数据模型
│   │   ├── user/                   # 用户模型
│   │   ├── project/                # 项目模型
│   │   └── file/                   # 文件模型
│   ├── services/                   # 服务层
│   │   ├── auth/                   # 认证服务
│   │   ├── file/                   # 文件服务
│   │   ├── email/                  # 邮件服务
│   │   └── storage/                # 存储服务
│   ├── middleware/                 # 中间件
│   ├── utils/                      # 工具类
│   ├── vendor/                     # 第三方库
│   └── views/                      # 视图模板
│
├── database/                       # 数据库相关
│   ├── migrations/                 # 数据库迁移文件
│   ├── seeds/                      # 数据填充文件
│   ├── backups/                    # 数据库备份
│   └── sql/                        # SQL脚本文件
│
├── config/                         # 配置文件
│   ├── database/                   # 数据库配置
│   ├── app/                        # 应用配置
│   ├── mail/                       # 邮件配置
│   └── upload/                     # 上传配置
│
├── uploads/                        # 上传文件存储
│   ├── projects/                   # 项目文件
│   ├── images/                     # 图片文件
│   ├── documents/                  # 文档文件
│   └── temp/                       # 临时文件
│
├── logs/                           # 日志文件
│   ├── error/                      # 错误日志
│   ├── access/                     # 访问日志
│   ├── upload/                     # 上传日志
│   └── system/                     # 系统日志
│
├── scripts/                        # 脚本文件
│   ├── deploy/                     # 部署脚本
│   ├── backup/                     # 备份脚本
│   └── maintenance/                # 维护脚本
│
└── docs/                           # 项目文档
    └── project-structure.md       # 本文档
```

## 架构设计原则

### 1. 模块化设计
- 前后端分离，职责清晰
- 按功能模块组织代码
- 便于团队协作开发

### 2. 可扩展性
- 预留API版本管理
- 支持插件式功能扩展
- 配置文件集中管理

### 3. 可维护性
- 统一的文件命名规范
- 清晰的目录层次结构
- 完善的日志记录系统

### 4. 安全性
- 上传文件分类存储
- 敏感配置独立管理
- 完整的权限控制体系

## 核心功能模块

### 用户管理模块
- 用户注册、登录、权限管理
- 个人资料管理
- 密码重置功能

### 项目管理模块
- 项目创建、编辑、删除
- 项目展示和搜索
- 项目分类管理

### 文件上传模块
- 代码文件上传
- 文件预览和下载
- 文件版本管理

### 内容管理模块
- 创业资讯发布
- 项目展示页面
- SEO优化支持

## 技术栈说明
- **前端**: HTML5 + CSS3 + JavaScript (ES6+)
- **后端**: PHP 7.4+ (MVC架构)
- **数据库**: MySQL 8.0+
- **服务器**: Apache/Nginx
- **部署**: 支持传统服务器部署
