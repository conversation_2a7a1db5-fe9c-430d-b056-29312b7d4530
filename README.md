# 创业项目管理平台

## 项目简介
一个基于 HTML、CSS、JS、PHP 和 MySQL 的创业项目管理平台，支持项目代码上传、管理和展示功能。

## 技术栈
- 前端：HTML5、CSS3、JavaScript (ES6+)
- 后端：PHP 7.4+
- 数据库：MySQL 8.0+
- 服务器：Apache/Nginx

## 项目结构
```
entrepreneurship/
├── frontend/           # 前端代码
├── backend/           # 后端代码
├── database/          # 数据库相关
├── config/           # 配置文件
├── uploads/          # 上传文件存储
├── logs/             # 日志文件
├── docs/             # 项目文档
└── scripts/          # 部署和维护脚本
```

## 部署说明
1. 将项目上传到服务器 `/www/wwwroot/CY` 目录
2. 配置数据库连接
3. 设置文件权限
4. 配置域名解析

## 开发规范
- 遵循 PSR-4 自动加载规范
- 使用 MVC 架构模式
- 前端采用模块化开发
- 数据库操作使用 PDO
