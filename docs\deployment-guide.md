# 部署指南

## 服务器环境要求

### 基础环境
- **操作系统**: Linux (推荐 Ubuntu 20.04+) 或 Windows Server
- **Web服务器**: Apache 2.4+ 或 Nginx 1.18+
- **PHP版本**: PHP 7.4+ (推荐 PHP 8.0+)
- **数据库**: MySQL 8.0+ 或 MariaDB 10.5+
- **内存**: 最低 2GB，推荐 4GB+
- **存储**: 最低 10GB，推荐 50GB+

### PHP扩展要求
```
- php-mysql
- php-pdo
- php-json
- php-mbstring
- php-curl
- php-gd
- php-zip
- php-fileinfo
```

## 部署步骤

### 1. 上传项目文件
将整个项目文件夹上传到服务器目录：
```bash
# 目标路径（根据你的服务器配置）
/www/wwwroot/CY/
```

### 2. 设置文件权限
```bash
# 设置基本权限
chmod -R 755 /www/wwwroot/CY/

# 设置上传目录权限
chmod -R 777 /www/wwwroot/CY/uploads/
chmod -R 777 /www/wwwroot/CY/logs/

# 设置配置文件权限
chmod 600 /www/wwwroot/CY/config/database/*
```

### 3. 配置数据库
1. 创建数据库和用户
2. 导入初始数据结构
3. 配置数据库连接信息

### 4. 配置Web服务器

#### Apache配置示例
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /www/wwwroot/CY/frontend
    
    <Directory /www/wwwroot/CY/frontend>
        AllowOverride All
        Require all granted
    </Directory>
    
    # API路由
    Alias /api /www/wwwroot/CY/backend/api
    <Directory /www/wwwroot/CY/backend/api>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog /www/wwwroot/CY/logs/error/apache_error.log
    CustomLog /www/wwwroot/CY/logs/access/apache_access.log combined
</VirtualHost>
```

#### Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /www/wwwroot/CY/frontend;
    index index.html index.php;
    
    # 静态文件处理
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API路由
    location /api/ {
        alias /www/wwwroot/CY/backend/api/;
        try_files $uri $uri/ /index.php?$query_string;
        
        location ~ \.php$ {
            fastcgi_pass 127.0.0.1:9000;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $request_filename;
            include fastcgi_params;
        }
    }
    
    # 日志配置
    access_log /www/wwwroot/CY/logs/access/nginx_access.log;
    error_log /www/wwwroot/CY/logs/error/nginx_error.log;
}
```

### 5. 域名解析配置
1. 在域名管理面板添加A记录
2. 指向服务器IP地址
3. 等待DNS生效（通常1-24小时）

### 6. SSL证书配置（推荐）
```bash
# 使用Let's Encrypt免费证书
certbot --nginx -d your-domain.com
```

## 安全配置

### 1. 隐藏敏感目录
```apache
# .htaccess 文件
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# 禁止访问配置文件
<FilesMatch "\.(ini|conf|config)$">
    Order allow,deny
    Deny from all
</FilesMatch>
```

### 2. 上传文件安全
- 限制上传文件类型
- 设置文件大小限制
- 文件名随机化处理
- 病毒扫描（可选）

### 3. 数据库安全
- 使用独立数据库用户
- 限制数据库用户权限
- 定期备份数据库
- 启用慢查询日志

## 性能优化

### 1. 启用Gzip压缩
```apache
# Apache
LoadModule deflate_module modules/mod_deflate.so
<Location />
    SetOutputFilter DEFLATE
</Location>
```

### 2. 设置缓存策略
```apache
# 静态资源缓存
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 month"
</FilesMatch>
```

### 3. 数据库优化
- 添加适当的索引
- 定期优化表结构
- 配置查询缓存
- 监控慢查询

## 监控和维护

### 1. 日志监控
- 定期检查错误日志
- 监控访问日志异常
- 设置日志轮转

### 2. 备份策略
- 每日自动备份数据库
- 每周备份完整项目文件
- 异地备份存储

### 3. 更新维护
- 定期更新PHP版本
- 及时修复安全漏洞
- 监控服务器资源使用

## 故障排除

### 常见问题
1. **500错误**: 检查PHP错误日志和文件权限
2. **数据库连接失败**: 验证数据库配置和权限
3. **文件上传失败**: 检查upload目录权限和PHP配置
4. **页面无法访问**: 检查Web服务器配置和DNS解析

### 调试模式
开发环境可以启用详细错误显示，生产环境务必关闭。
